package com.voice.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * HTTP客户端配置
 * 专门用于处理长时间的语音合成请求
 */
@Configuration
public class HttpClientConfig {

    @Bean(name = "longTimeoutRestTemplate")
    public RestTemplate longTimeoutRestTemplate() {
        // 2小时超时配置
        int timeout = 7200000; // 2小时 = 7200秒 = 7200000毫秒
        
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(timeout)           // 连接超时
                .setConnectionRequestTimeout(timeout) // 从连接池获取连接的超时时间
                .setSocketTimeout(timeout)            // 数据读取超时
                .build();
        
        CloseableHttpClient client = HttpClientBuilder.create()
                .setDefaultRequestConfig(config)
                .setMaxConnTotal(100)                 // 最大连接数
                .setMaxConnPerRoute(20)               // 每个路由的最大连接数
                .build();
        
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(client);
        factory.setConnectTimeout(timeout);
        factory.setReadTimeout(timeout);
        
        return new RestTemplate(factory);
    }
}
